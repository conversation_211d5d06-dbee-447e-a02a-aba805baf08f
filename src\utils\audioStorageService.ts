import { supabase } from './supabaseClient';
import encryptionService from './encryptionService';
import type {
  AudioBlob,
  AudioMessage,
  AudioMessageMetadata,
  StorageResult,
  ValidationResult,
  IntegrityVerificationResult,
  ChecksumResult,
  DataRecoveryResult,
  BackupResult,
  EncryptedAudioData,
  DecryptedAudioData,
  AudioMetadata,
  SecurityValidationResult,
  BasicValidationResult,
  FormatValidationResult,
  QualityValidationResult,
  RealtimeValidationResult,
  ValidationOptions,
  AudioQuality
} from '../types/audio';

interface AudioStorageConfig {
  readonly dbName: string;
  readonly dbVersion: number;
  readonly storeName: string;
  readonly maxRetries: number;
  readonly syncInterval: number;
}

type AudioQualitySettings = {
  readonly [key in AudioQuality]: {
    readonly sampleRate: number;
    readonly bitRate: number;
    readonly quality: number;
  };
};

interface SyncStatus {
  readonly pending: number;
  readonly synced: number;
  readonly total: number;
  readonly syncPercentage: number;
}

interface MessageData {
  readonly sessionId: string;
  readonly userId: string;
  readonly speakerId: string;
  readonly speakerName: string;
  readonly messageType?: 'user_voice' | 'agent_voice' | 'async_voice' | 'session_audio';
  readonly duration?: number;
  readonly quality?: AudioQuality;
  readonly transcription?: string;
  readonly confidence?: number;
  readonly sessionToken: string;
}

/**
 * SECURE Audio Storage Service with AES-256 Encryption (TypeScript)
 * Handles HIPAA-compliant audio message storage, retrieval, and management
 * 
 * SECURITY FEATURES:
 * - AES-256-GCM encryption for all audio data
 * - Secure key derivation from session tokens
 * - Encrypted storage in both local IndexedDB and cloud
 * - Comprehensive audit logging
 * - Data integrity validation with SHA-256 checksums
 * - Type-safe operations with comprehensive error handling
 */
class AudioStorageService {
  private readonly config: AudioStorageConfig;
  private readonly audioQuality: AudioQualitySettings;
  private db: IDBDatabase | null = null;
  private syncInProgress = false;
  private lastSyncTime: Date | null = null;

  constructor() {
    this.config = {
      dbName: 'VoiceHealthAudioDB',
      dbVersion: 2,
      storeName: 'audioMessages',
      maxRetries: 3,
      syncInterval: 30000 // 30 seconds
    };

    this.audioQuality = {
      low: { sampleRate: 16000, bitRate: 32000, quality: 0.3 },
      medium: { sampleRate: 22050, bitRate: 64000, quality: 0.5 },
      high: { sampleRate: 44100, bitRate: 128000, quality: 0.7 }
    } as const;
  }

  /**
   * Initialize IndexedDB for local storage
   */
  async initializeDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.dbName, this.config.dbVersion);
      
      request.onerror = () => {
        console.error('Failed to open IndexedDB:', request.error);
        reject(new Error('Failed to initialize audio storage database'));
      };
      
      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ Audio storage database initialized');
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object store for audio messages
        if (!db.objectStoreNames.contains(this.config.storeName)) {
          const store = db.createObjectStore(this.config.storeName, { keyPath: 'id' });
          store.createIndex('sessionId', 'sessionId', { unique: false });
          store.createIndex('userId', 'userId', { unique: false });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('status', 'status', { unique: false });
        }
      };
    });
  }

  /**
   * Store audio message locally and in cloud with AES-256 encryption
   */
  async storeAudioMessage(audioBlob: AudioBlob, messageData: MessageData): Promise<StorageResult> {
    try {
      if (!this.db) await this.initializeDB();

      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const timestamp = new Date().toISOString();

      // Validate session token for encryption
      if (!messageData.sessionToken) {
        throw new Error('Session token is required for secure audio storage');
      }

      // Compress audio based on quality setting
      const compressedBlob = await this.compressAudio(audioBlob, messageData.quality || 'medium');

      // Convert blob to array buffer for encryption
      const audioBuffer = await compressedBlob.arrayBuffer();
      
      // Generate SHA-256 checksum before encryption
      console.log('🔍 Generating audio checksum...');
      const originalChecksum = await this.generateAudioChecksum(audioBuffer);
      
      // Encrypt audio data using AES-256-GCM
      console.log('🔒 Encrypting audio data...');
      const encryptedMedicalData = await encryptionService.encryptMedicalData(
        {
          audioData: Array.from(new Uint8Array(audioBuffer)),
          originalSize: compressedBlob.size,
          mimeType: compressedBlob.type,
          checksum: originalChecksum
        },
        messageData.sessionToken
      );

      // Transform to EncryptedAudioData format
      const encryptedAudioData: EncryptedAudioData = {
        encrypted: encryptedMedicalData.encrypted || true,
        algorithm: 'AES-256-GCM',
        keyLength: 256,
        iv: encryptedMedicalData.iv || '',
        authTag: encryptedMedicalData.authTag || '',
        encryptedData: encryptedMedicalData.encryptedData || JSON.stringify(encryptedMedicalData),
        timestamp: new Date().toISOString()
      };

      // Generate checksum of encrypted data for integrity verification
      const encryptedChecksum = await this.generateDataChecksum(JSON.stringify(encryptedAudioData));

      // Create message object with encrypted audio
      const audioMessage: AudioMessage = {
        id: messageId,
        sessionId: messageData.sessionId,
        userId: messageData.userId,
        speakerId: messageData.speakerId,
        speakerName: messageData.speakerName,
        messageType: messageData.messageType || 'user_voice',
        encryptedAudioData: encryptedAudioData,
        duration: messageData.duration || 0,
        quality: messageData.quality || 'medium',
        transcription: messageData.transcription || undefined,
        confidence: messageData.confidence || 0.95,
        timestamp,
        status: 'pending_sync',
        size: compressedBlob.size,
        encrypted: encryptedAudioData.encrypted,
        originalChecksum: originalChecksum,
        encryptedChecksum: encryptedChecksum,
        checksumAlgorithm: 'SHA-256',
        metadata: {
          sampleRate: this.audioQuality[messageData.quality || 'medium'].sampleRate,
          bitRate: this.audioQuality[messageData.quality || 'medium'].bitRate,
          format: 'webm',
          codec: 'opus',
          encryption: {
            algorithm: encryptedAudioData.algorithm,
            keyLength: encryptedAudioData.keyLength,
            encrypted: encryptedAudioData.encrypted,
            timestamp: encryptedAudioData.timestamp
          },
          integrity: {
            originalChecksum: originalChecksum,
            encryptedChecksum: encryptedChecksum,
            algorithm: 'SHA-256',
            verified: false
          }
        }
      };

      // Store locally first
      await this.storeLocalMessage(audioMessage);

      // Upload encrypted audio to cloud storage (Supabase)
      const cloudUrl = await this.uploadEncryptedToCloud(encryptedAudioData, messageId, messageData);
      if (cloudUrl) {
        // Update local storage with cloud URL and sync status
        await this.updateLocalMessage(messageId, { status: 'synced' });
      }

      // Store message metadata in database (without sensitive audio data)
      await this.storeMessageMetadata(audioMessage);

      // Log secure storage event
      await this.logSecureStorageEvent('audio_stored', messageId, {
        encrypted: encryptedAudioData.encrypted,
        size: compressedBlob.size,
        cloud_stored: !!cloudUrl
      });

      return {
        success: true,
        messageId,
        localStored: true,
        cloudStored: !!cloudUrl,
        size: compressedBlob.size,
        encrypted: encryptedAudioData.encrypted
      };

    } catch (error) {
      console.error('❌ Failed to store audio message:', error);
      throw error;
    }
  }

  /**
   * Compress audio based on quality setting
   */
  private async compressAudio(audioBlob: AudioBlob, quality: AudioQuality): Promise<AudioBlob> {
    try {
      // For now, return the original blob
      // In production, implement actual audio compression based on quality settings
      return audioBlob;
    } catch (error) {
      console.error('Audio compression failed:', error);
      return audioBlob; // Return original if compression fails
    }
  }

  /**
   * Store message locally in IndexedDB
   */
  private async storeLocalMessage(audioMessage: AudioMessage): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction([this.config.storeName], 'readwrite');
      const store = transaction.objectStore(this.config.storeName);
      const request = store.add(audioMessage);
      
      request.onsuccess = () => {
        console.log('✅ Audio message stored locally:', audioMessage.id);
        resolve();
      };
      
      request.onerror = () => {
        console.error('❌ Failed to store audio message locally:', request.error);
        reject(new Error('Failed to store audio message locally'));
      };
    });
  }

  /**
   * Update local message with new data
   */
  private async updateLocalMessage(messageId: string, updates: Partial<AudioMessage>): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction([this.config.storeName], 'readwrite');
      const store = transaction.objectStore(this.config.storeName);
      const getRequest = store.get(messageId);
      
      getRequest.onsuccess = () => {
        const existingMessage = getRequest.result;
        if (existingMessage) {
          const updatedMessage = { ...existingMessage, ...updates };
          const putRequest = store.put(updatedMessage);
          
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(new Error('Failed to update message'));
        } else {
          reject(new Error('Message not found'));
        }
      };
      
      getRequest.onerror = () => reject(new Error('Failed to retrieve message for update'));
    });
  }

  /**
   * Generate SHA-256 checksum for audio data
   */
  async generateAudioChecksum(audioBuffer: ArrayBuffer): Promise<string> {
    try {
      const hashBuffer = await crypto.subtle.digest('SHA-256', audioBuffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      return hashHex;
    } catch (error) {
      console.error('Failed to generate audio checksum:', error);
      throw new Error('Checksum generation failed');
    }
  }

  /**
   * Generate SHA-256 checksum for any data
   */
  async generateDataChecksum(data: string): Promise<string> {
    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      return hashHex;
    } catch (error) {
      console.error('Failed to generate data checksum:', error);
      throw new Error('Checksum generation failed');
    }
  }

  /**
   * Upload encrypted audio to Supabase cloud storage
   */
  private async uploadEncryptedToCloud(
    encryptedAudioData: EncryptedAudioData,
    messageId: string,
    messageData: MessageData
  ): Promise<string | null> {
    try {
      const fileName = `encrypted-audio/${messageData.userId}/${messageData.sessionId}/${messageId}.enc`;

      // Convert encrypted data to blob for upload
      const encryptedBlob = new Blob([JSON.stringify(encryptedAudioData)], {
        type: 'application/json'
      });

      const { data, error } = await supabase.storage
        .from('voice-messages')
        .upload(fileName, encryptedBlob, {
          contentType: 'application/json',
          metadata: {
            messageId,
            sessionId: messageData.sessionId,
            userId: messageData.userId,
            duration: messageData.duration?.toString() || '0',
            encrypted: 'true',
            algorithm: encryptedAudioData.algorithm,
            encryption_timestamp: encryptedAudioData.timestamp
          }
        });

      if (error) {
        console.error('Encrypted cloud upload failed:', error);
        return null;
      }

      // Get public URL (encrypted data is safe to be public)
      const { data: urlData } = supabase.storage
        .from('voice-messages')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Encrypted cloud upload error:', error);
      return null;
    }
  }

  /**
   * Store message metadata in database (without sensitive audio data)
   */
  private async storeMessageMetadata(audioMessage: AudioMessage): Promise<void> {
    try {
      const { error } = await supabase
        .from('voice_messages')
        .insert({
          id: audioMessage.id,
          session_id: audioMessage.sessionId,
          user_id: audioMessage.userId,
          speaker_id: audioMessage.speakerId,
          speaker_name: audioMessage.speakerName,
          message_type: audioMessage.messageType,
          cloud_url: null, // Cloud URL stored separately
          duration: audioMessage.duration,
          quality: audioMessage.quality,
          transcription: audioMessage.transcription,
          confidence: audioMessage.confidence,
          status: audioMessage.status,
          size: audioMessage.size,
          encrypted: audioMessage.encrypted,
          original_checksum: audioMessage.originalChecksum,
          encrypted_checksum: audioMessage.encryptedChecksum,
          checksum_algorithm: audioMessage.checksumAlgorithm,
          integrity_verified: false,
          metadata: audioMessage.metadata,
          created_at: audioMessage.timestamp
        });

      if (error) {
        console.error('Failed to store message metadata:', error);
      }
    } catch (error) {
      console.error('Database error:', error);
    }
  }

  /**
   * Retrieve and decrypt audio message
   */
  async getDecryptedAudioMessage(messageId: string, sessionToken: string): Promise<AudioMessage & { audioBlob: AudioBlob; integrityVerified: boolean }> {
    try {
      if (!sessionToken) {
        throw new Error('Session token is required for audio decryption');
      }

      // Get encrypted message from local storage first
      let audioMessage = await this.getLocalMessage(messageId);

      // If not found locally, try to download from cloud
      if (!audioMessage) {
        audioMessage = await this.downloadFromCloud(messageId);
      }

      if (!audioMessage || !audioMessage.encryptedAudioData) {
        throw new Error('Audio message not found or not encrypted');
      }

      // Decrypt audio data
      console.log('🔓 Decrypting audio data...');

      // Transform EncryptedAudioData to format expected by encryptionService
      const encryptedMedicalData = {
        encrypted: audioMessage.encryptedAudioData.encrypted,
        iv: audioMessage.encryptedAudioData.iv,
        authTag: audioMessage.encryptedAudioData.authTag,
        encryptedData: audioMessage.encryptedAudioData.encryptedData,
        algorithm: audioMessage.encryptedAudioData.algorithm,
        keyLength: audioMessage.encryptedAudioData.keyLength,
        timestamp: audioMessage.encryptedAudioData.timestamp
      };

      const decryptedData = await encryptionService.decryptMedicalData(
        encryptedMedicalData,
        sessionToken
      );

      // Transform decrypted data to DecryptedAudioData format
      const decryptedAudioData: DecryptedAudioData = {
        audioData: (decryptedData as any).audioData || [],
        originalSize: (decryptedData as any).originalSize || 0,
        mimeType: (decryptedData as any).mimeType || 'audio/webm',
        checksum: (decryptedData as any).checksum || ''
      };

      // Verify data integrity using checksums
      const integrityResult = await this.verifyAudioIntegrity(audioMessage, decryptedAudioData);

      if (!integrityResult.valid) {
        console.error('❌ Audio data integrity check failed:', integrityResult.errors);

        // Attempt data recovery for corrupted data
        const recoveryResult = await this.handleDataCorruption(messageId, integrityResult);

        if (!recoveryResult.success) {
          throw new Error(`Audio data integrity check failed: ${integrityResult.errors.join(', ')}`);
        }

        // Use recovered data
        const recoveredData = recoveryResult.data;
        const audioArray = new Uint8Array(recoveredData.encryptedAudioData.encryptedData as any);
        const audioBlob = new Blob([audioArray], { type: decryptedAudioData.mimeType }) as AudioBlob;

        return {
          ...recoveredData,
          audioBlob,
          decrypted: true,
          recovered: true,
          recoverySource: recoveryResult.source,
          integrityResult,
          integrityVerified: false
        } as any;
      }

      // Convert decrypted data back to blob
      const audioArray = new Uint8Array(decryptedAudioData.audioData);
      const audioBlob = new Blob([audioArray], { type: decryptedAudioData.mimeType }) as AudioBlob;

      return {
        ...audioMessage,
        audioBlob,
        decrypted: true,
        originalSize: decryptedAudioData.originalSize,
        integrityVerified: true,
        integrityResult
      } as any;

    } catch (error) {
      console.error('Failed to decrypt audio message:', error);
      throw error;
    }
  }

  /**
   * Get local message from IndexedDB
   */
  private async getLocalMessage(messageId: string): Promise<AudioMessage | null> {
    return new Promise((resolve) => {
      if (!this.db) {
        resolve(null);
        return;
      }

      const transaction = this.db.transaction([this.config.storeName], 'readonly');
      const store = transaction.objectStore(this.config.storeName);
      const request = store.get(messageId);

      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => resolve(null);
    });
  }

  /**
   * Download encrypted audio from cloud storage
   */
  private async downloadFromCloud(messageId: string): Promise<AudioMessage | null> {
    try {
      // Get message metadata from database
      const { data: messageData, error } = await supabase
        .from('voice_messages')
        .select('*')
        .eq('id', messageId)
        .single();

      if (error || !messageData) {
        console.error('Message metadata not found:', error);
        return null;
      }

      if (!messageData.cloud_url) {
        console.error('No cloud URL for message:', messageId);
        return null;
      }

      // Download encrypted data from cloud
      const response = await fetch(messageData.cloud_url);
      if (!response.ok) {
        throw new Error(`Failed to download encrypted audio: ${response.status}`);
      }

      const encryptedAudioData = await response.json();

      return {
        ...messageData,
        encryptedAudioData
      } as AudioMessage;

    } catch (error) {
      console.error('Failed to download from cloud:', error);
      return null;
    }
  }

  /**
   * Log secure storage events for audit trail
   */
  private async logSecureStorageEvent(eventType: string, messageId: string, details: Record<string, unknown>): Promise<void> {
    try {
      await supabase.from('audit_logs').insert({
        event_type: eventType,
        resource_type: 'audio_message',
        resource_id: messageId,
        action: 'storage_operation',
        details: {
          ...details,
          timestamp: new Date().toISOString(),
          service: 'audio_storage'
        }
      });
    } catch (error) {
      console.error('Failed to log storage event:', error);
    }
  }

  /**
   * Verify audio data integrity using checksums
   */
  async verifyAudioIntegrity(audioMessage: AudioMessage, decryptedAudioData: DecryptedAudioData): Promise<IntegrityVerificationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let originalChecksumMatch = false;
    let encryptedChecksumMatch = false;

    try {
      // Verify original audio checksum
      if (audioMessage.originalChecksum && decryptedAudioData.checksum) {
        originalChecksumMatch = audioMessage.originalChecksum === decryptedAudioData.checksum;

        if (!originalChecksumMatch) {
          errors.push('Original audio data integrity check failed - data may be corrupted');
        }
      } else {
        warnings.push('Original checksum not available for verification');
      }

      // Verify encrypted data checksum
      if (audioMessage.encryptedChecksum && audioMessage.encryptedAudioData) {
        const currentEncryptedChecksum = await this.generateDataChecksum(JSON.stringify(audioMessage.encryptedAudioData));
        encryptedChecksumMatch = audioMessage.encryptedChecksum === currentEncryptedChecksum;

        if (!encryptedChecksumMatch) {
          errors.push('Encrypted audio data integrity check failed - data may have been tampered with');
        }
      } else {
        warnings.push('Encrypted checksum not available for verification');
      }

      const integrityResult: IntegrityVerificationResult = {
        valid: originalChecksumMatch && encryptedChecksumMatch,
        originalChecksumMatch,
        encryptedChecksumMatch,
        errors,
        warnings
      };

      // Log integrity check results
      await this.logIntegrityCheck(audioMessage.id, integrityResult);

      return integrityResult;

    } catch (error) {
      errors.push(`Integrity verification failed: ${(error as Error).message}`);
      return {
        valid: false,
        originalChecksumMatch,
        encryptedChecksumMatch,
        errors,
        warnings
      };
    }
  }

  /**
   * Handle data corruption with recovery mechanisms
   */
  private async handleDataCorruption(messageId: string, integrityResult: IntegrityVerificationResult): Promise<DataRecoveryResult> {
    try {
      console.error('🚨 Data corruption detected for message:', messageId);

      // Log critical security event
      await this.logSecureStorageEvent('data_corruption_detected', messageId, {
        integrity_result: integrityResult,
        severity: 'critical',
        requires_investigation: true
      });

      // For now, return failure - in production, implement actual recovery
      return {
        success: false,
        source: 'local_backup',
        data: {} as AudioMessage,
        integrityVerified: false,
        recoveryTime: 0
      };

    } catch (error) {
      console.error('Failed to handle data corruption:', error);
      return {
        success: false,
        source: 'local_backup',
        data: {} as AudioMessage,
        integrityVerified: false,
        recoveryTime: 0
      };
    }
  }

  /**
   * Log integrity check results
   */
  private async logIntegrityCheck(messageId: string, integrityResult: IntegrityVerificationResult): Promise<void> {
    try {
      await this.logSecureStorageEvent('integrity_check', messageId, {
        original_checksum_match: integrityResult.originalChecksumMatch,
        encrypted_checksum_match: integrityResult.encryptedChecksumMatch,
        overall_valid: integrityResult.valid,
        errors: integrityResult.errors,
        warnings: integrityResult.warnings,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to log integrity check:', error);
    }
  }

  /**
   * COMPREHENSIVE AUDIO FILE VALIDATION
   * Enhanced validation with security checks and malicious file detection
   */
  async validateAudioFile(audioBlob: AudioBlob, options: ValidationOptions = {}): Promise<ValidationResult> {
    // Create mutable arrays for building the result
    const errors: string[] = [];
    const warnings: string[] = [];
    let metadata: AudioMetadata = {
      sampleRate: 0,
      duration: 0,
      channels: 0,
      rms: '0',
      peak: '0',
      dynamicRange: '0',
      format: 'unknown',
      codec: 'unknown'
    };
    let securityChecks: SecurityValidationResult = {
      safe: true,
      threats: [],
      checks: {
        headerValidation: false,
        metadataValidation: false,
        contentValidation: false,
        sizeConsistency: false
      }
    };

    try {
      // Basic file validation
      const basicValidation = this.validateBasicFileProperties(audioBlob);
      if (!basicValidation.valid) {
        errors.push(...basicValidation.errors);
        return {
          valid: false,
          errors,
          warnings,
          metadata,
          securityChecks
        };
      }

      // Audio format validation
      const formatValidation = await this.validateAudioFormat(audioBlob);
      if (!formatValidation.valid) {
        errors.push(...formatValidation.errors);
        warnings.push(...formatValidation.warnings);
      }

      // Security validation (malicious file detection)
      const securityValidation = await this.validateAudioSecurity(audioBlob);
      securityChecks = securityValidation;
      if (!securityValidation.safe) {
        errors.push(...securityValidation.threats);
      }

      // Audio quality validation
      const qualityValidation = await this.validateAudioQuality(audioBlob);
      metadata = qualityValidation.metadata;
      if (!qualityValidation.acceptable) {
        warnings.push(...qualityValidation.warnings);
      }

      // Real-time validation for streaming audio
      if (options.realTime) {
        const realtimeValidation = this.validateRealtimeAudio(audioBlob);
        if (!realtimeValidation.valid) {
          errors.push(...realtimeValidation.errors);
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        metadata,
        securityChecks
      };

    } catch (error) {
      errors.push(`Validation failed: ${(error as Error).message}`);
      return {
        valid: false,
        errors,
        warnings,
        metadata,
        securityChecks
      };
    }
  }

  /**
   * Validate basic file properties
   */
  validateBasicFileProperties(audioBlob: AudioBlob): BasicValidationResult {
    const errors: string[] = [];

    // File size validation
    const maxSize = 25 * 1024 * 1024; // 25MB
    const minSize = 1024; // 1KB minimum

    if (audioBlob.size > maxSize) {
      errors.push(`Audio file too large: ${(audioBlob.size / (1024 * 1024)).toFixed(2)}MB. Maximum allowed: ${maxSize / (1024 * 1024)}MB`);
    }

    if (audioBlob.size < minSize) {
      errors.push(`Audio file too small: ${audioBlob.size} bytes. Minimum required: ${minSize} bytes`);
    }

    // MIME type validation
    const allowedTypes = [
      'audio/webm',
      'audio/wav',
      'audio/wave',
      'audio/mp3',
      'audio/mpeg',
      'audio/m4a',
      'audio/mp4',
      'audio/ogg',
      'audio/opus'
    ];

    if (!allowedTypes.includes(audioBlob.type)) {
      errors.push(`Unsupported audio format: ${audioBlob.type}. Supported formats: ${allowedTypes.join(', ')}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate audio format and codec
   */
  async validateAudioFormat(audioBlob: AudioBlob): Promise<FormatValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Create audio context for format analysis
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const arrayBuffer = await audioBlob.arrayBuffer();

      try {
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer.slice(0));

        // Validate sample rate
        const sampleRate = audioBuffer.sampleRate;
        if (sampleRate < 8000 || sampleRate > 48000) {
          warnings.push(`Unusual sample rate: ${sampleRate}Hz. Recommended: 16kHz-48kHz`);
        }

        // Validate channel count
        const channels = audioBuffer.numberOfChannels;
        if (channels > 2) {
          warnings.push(`High channel count: ${channels}. Mono or stereo recommended for medical consultations`);
        }

        // Validate duration
        const duration = audioBuffer.duration;
        const maxDuration = 10 * 60; // 10 minutes
        if (duration > maxDuration) {
          errors.push(`Audio too long: ${duration.toFixed(1)}s. Maximum allowed: ${maxDuration}s`);
        }

        if (duration < 0.1) {
          errors.push(`Audio too short: ${duration.toFixed(3)}s. Minimum required: 0.1s`);
        }

      } catch (decodeError) {
        errors.push(`Invalid audio format: Cannot decode audio data - ${(decodeError as Error).message}`);
      }

      await audioContext.close();

    } catch (error) {
      warnings.push(`Format validation limited: ${(error as Error).message}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate audio security (malicious file detection)
   */
  async validateAudioSecurity(audioBlob: AudioBlob): Promise<SecurityValidationResult> {
    const threats: string[] = [];
    let safe = true;
    const checks = {
      headerValidation: false,
      metadataValidation: false,
      contentValidation: false,
      sizeConsistency: false
    };

    try {
      const arrayBuffer = await audioBlob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      // Header validation
      checks.headerValidation = this.validateAudioHeader(uint8Array, audioBlob.type);
      if (!checks.headerValidation) {
        threats.push('Invalid audio file header detected');
        safe = false;
      }

      // Metadata validation (check for suspicious embedded content)
      checks.metadataValidation = this.validateAudioMetadata(uint8Array);
      if (!checks.metadataValidation) {
        threats.push('Suspicious metadata detected in audio file');
        safe = false;
      }

      // Content validation (check for embedded executables)
      checks.contentValidation = this.validateAudioContent(uint8Array);
      if (!checks.contentValidation) {
        threats.push('Suspicious content detected in audio file');
        safe = false;
      }

      // Size consistency check
      checks.sizeConsistency = this.validateSizeConsistency(audioBlob, uint8Array);
      if (!checks.sizeConsistency) {
        threats.push('File size inconsistency detected');
        safe = false;
      }

    } catch (error) {
      threats.push(`Security validation failed: ${(error as Error).message}`);
      safe = false;
    }

    return {
      safe,
      threats,
      checks
    };
  }

  /**
   * Validate audio header signatures
   */
  private validateAudioHeader(uint8Array: Uint8Array, mimeType: string): boolean {
    const headerSignatures: Record<string, number[]> = {
      'audio/webm': [0x1A, 0x45, 0xDF, 0xA3], // WebM signature
      'audio/wav': [0x52, 0x49, 0x46, 0x46], // RIFF signature
      'audio/wave': [0x52, 0x49, 0x46, 0x46], // RIFF signature
      'audio/mp3': [0xFF, 0xFB], // MP3 frame header (partial)
      'audio/mpeg': [0xFF, 0xFB], // MPEG audio frame header
      'audio/ogg': [0x4F, 0x67, 0x67, 0x53], // OggS signature
      'audio/m4a': [0x66, 0x74, 0x79, 0x70], // ftyp box (partial)
      'audio/mp4': [0x66, 0x74, 0x79, 0x70] // ftyp box (partial)
    };

    const expectedSignature = headerSignatures[mimeType];
    if (!expectedSignature) return true; // Unknown type, skip validation

    // Check if file starts with expected signature
    for (let i = 0; i < expectedSignature.length && i < uint8Array.length; i++) {
      if (uint8Array[i] !== expectedSignature[i]) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validate audio metadata for suspicious content
   */
  private validateAudioMetadata(uint8Array: Uint8Array): boolean {
    // Check for suspicious patterns that might indicate embedded content
    const suspiciousPatterns = [
      [0x4D, 0x5A], // MZ (executable header)
      [0x50, 0x4B], // PK (ZIP/JAR header)
      [0x7F, 0x45, 0x4C, 0x46], // ELF executable
      [0xCA, 0xFE, 0xBA, 0xBE], // Java class file
      [0x3C, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74], // <script tag
      [0x3C, 0x68, 0x74, 0x6D, 0x6C], // <html tag
    ];

    for (const pattern of suspiciousPatterns) {
      if (this.findPattern(uint8Array, pattern)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validate audio content for embedded threats
   */
  private validateAudioContent(uint8Array: Uint8Array): boolean {
    // Check for excessive null bytes (potential padding for hidden content)
    let nullByteCount = 0;
    const sampleSize = Math.min(1024, uint8Array.length);

    for (let i = 0; i < sampleSize; i++) {
      if (uint8Array[i] === 0) {
        nullByteCount++;
      }
    }

    // If more than 50% null bytes in sample, suspicious
    if (nullByteCount / sampleSize > 0.5) {
      return false;
    }

    return true;
  }

  /**
   * Validate size consistency
   */
  private validateSizeConsistency(audioBlob: AudioBlob, uint8Array: Uint8Array): boolean {
    return audioBlob.size === uint8Array.length;
  }

  /**
   * Find pattern in byte array
   */
  private findPattern(uint8Array: Uint8Array, pattern: number[]): boolean {
    for (let i = 0; i <= uint8Array.length - pattern.length; i++) {
      let found = true;
      for (let j = 0; j < pattern.length; j++) {
        if (uint8Array[i + j] !== pattern[j]) {
          found = false;
          break;
        }
      }
      if (found) return true;
    }
    return false;
  }

  /**
   * Validate audio quality thresholds
   */
  async validateAudioQuality(audioBlob: AudioBlob): Promise<QualityValidationResult> {
    const warnings: string[] = [];
    let metadata: AudioMetadata = {
      sampleRate: 0,
      duration: 0,
      channels: 0,
      rms: '0',
      peak: '0',
      dynamicRange: '0',
      format: audioBlob.type.split('/')[1] || 'unknown',
      codec: 'unknown'
    };

    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer.slice(0));

      // Analyze audio characteristics
      const channelData = audioBuffer.getChannelData(0);
      const sampleRate = audioBuffer.sampleRate;
      const duration = audioBuffer.duration;

      // Calculate RMS (Root Mean Square) for volume analysis
      let rms = 0;
      for (let i = 0; i < channelData.length; i++) {
        rms += (channelData[i] || 0) * (channelData[i] || 0);
      }
      rms = Math.sqrt(rms / channelData.length);

      // Calculate peak amplitude
      let peak = 0;
      for (let i = 0; i < channelData.length; i++) {
        peak = Math.max(peak, Math.abs(channelData[i] || 0));
      }

      // Store metadata
      metadata = {
        sampleRate,
        duration,
        channels: audioBuffer.numberOfChannels,
        rms: rms.toFixed(4),
        peak: peak.toFixed(4),
        dynamicRange: (peak / (rms + 0.001)).toFixed(2), // Avoid division by zero
        format: audioBlob.type.split('/')[1] || 'unknown',
        codec: 'unknown'
      };

      // Quality checks
      if (rms < 0.01) {
        warnings.push('Audio level very low - may affect transcription quality');
      }

      if (peak > 0.95) {
        warnings.push('Audio may be clipped - consider reducing input gain');
      }

      if (sampleRate < 16000) {
        warnings.push('Low sample rate may affect transcription accuracy');
      }

      await audioContext.close();

    } catch (error) {
      warnings.push(`Quality analysis failed: ${(error as Error).message}`);
    }

    return {
      acceptable: warnings.length === 0,
      warnings,
      metadata
    };
  }

  /**
   * Validate real-time audio chunks
   */
  validateRealtimeAudio(audioBlob: AudioBlob): RealtimeValidationResult {
    const errors: string[] = [];

    // Check chunk size for real-time processing
    const maxChunkSize = 1024 * 1024; // 1MB chunks
    if (audioBlob.size > maxChunkSize) {
      errors.push(`Audio chunk too large for real-time processing: ${audioBlob.size} bytes. Maximum: ${maxChunkSize} bytes`);
    }

    // Check for minimum chunk size
    const minChunkSize = 1024; // 1KB minimum
    if (audioBlob.size < minChunkSize) {
      errors.push(`Audio chunk too small: ${audioBlob.size} bytes. Minimum: ${minChunkSize} bytes`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate user-friendly error messages
   */
  generateUserFriendlyErrorMessage(validationResult: ValidationResult): string {
    if (validationResult.valid) {
      return 'Audio file is valid and ready for processing.';
    }

    const criticalErrors = validationResult.errors.filter(error =>
      error.includes('too large') ||
      error.includes('Unsupported') ||
      error.includes('Invalid audio format') ||
      error.includes('Security')
    );

    if (criticalErrors.length > 0) {
      return `Unable to process audio file: ${criticalErrors[0]}. Please check your audio file and try again.`;
    }

    return `Audio file has issues: ${validationResult.errors[0]}. Please verify your recording and try again.`;
  }

  /**
   * Get sync status for all messages
   */
  async getSyncStatus(): Promise<SyncStatus> {
    try {
      if (!this.db) await this.initializeDB();

      return new Promise((resolve) => {
        if (!this.db) {
          resolve({ pending: 0, synced: 0, total: 0, syncPercentage: 100 });
          return;
        }

        const transaction = this.db.transaction([this.config.storeName], 'readonly');
        const store = transaction.objectStore(this.config.storeName);
        const request = store.getAll();

        request.onsuccess = () => {
          const messages = request.result;
          const pending = messages.filter((msg: AudioMessage) => msg.status === 'pending_sync').length;
          const synced = messages.filter((msg: AudioMessage) => msg.status === 'synced').length;

          resolve({
            pending,
            synced,
            total: messages.length,
            syncPercentage: messages.length > 0 ? (synced / messages.length) * 100 : 100
          });
        };
        request.onerror = () => resolve({ pending: 0, synced: 0, total: 0, syncPercentage: 100 });
      });
    } catch (error) {
      return { pending: 0, synced: 0, total: 0, syncPercentage: 100 };
    }
  }
}

// Export singleton instance
export default new AudioStorageService();
